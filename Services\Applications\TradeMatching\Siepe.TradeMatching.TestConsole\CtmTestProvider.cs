﻿using Siepe.TradeMatching.Entities;
using Siepe.TradeMatching.Entities.CTM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.TradeMatching.Tests
{
    public class CtmTestProvider
    {
        public AllocationStatus GetAllocationStatus()
        {
            return new AllocationStatus
            {
                AllocationIdentifier = new AllocationIdentifier
                {
                    AllocationReferenceNumber = "SP104689",
                    BlockReferenceNumber = "BB3850371"
                },

                BlockCompletionStatus = BlockCompletionStatus.Incomplete,
                InternalStatus = InternalStatus.New,
                MatchAgreeStatus = MatchAgreeStatus.NotMatchAgreed,
                MatchStatus = MatchStatus.Unmatched,
                VersionNumber = 1
            };
        }

        public Allocation GetMatchTestAllocation()
        {
            return new Allocation
            {
                InstId = 20402,
                MasterReference = "BB3967665",
                //ClientAllocationReference = "SP125226",
                BlockQuantity = 11630,
                BrokerIdentifierType = "BicCode",
                ExecutingBroker = "AUTOBKMAXXX",//"AUTOBKMISMX",//"AUTOBKMAXXX",
                ClearingBroker  = "AUTOBKMAXXX",//"AUTOBKMISMX",//"AUTOBKMAXXX",
                AllocationQuantity = 11630,
                TradeDate = DateTime.Now,
                SettleDate = DateTime.Now.AddDays(3),
                TradeCurrencyCode = "GBP",
                Side = "B",
                Price = 29.7166m,
                SecurityIdentifierType = "Isin",
                SecurityIdentifier = "BMG0464B1072",
                Description = "ARGO",
                BlockGrossAmount = 345604.058m,//0.9846330000000000m,
                AllocationGrossAmount = 345604.058m,//748424.4700000000000000m,
                TotCommission = 0m,
                SecFeeAmount = 0m,
                OtherFees = 0m,
                PortfolioId = 1064
            };
        }

        public Allocation GetMisMatchTestAllocation()
        {
            return new Allocation
            {
                InstId = 20402,
                //MasterReference = "BB3967665",
                //ClientAllocationReference = "SP125226",
                BlockQuantity = 11634,
                BrokerIdentifierType = "BicCode",
                ExecutingBroker = "BKBLMISMAMT",
                ClearingBroker = "BKBLMISMAMT",
                AllocationQuantity = 760105,
                TradeDate = DateTime.Now,
                SettleDate = DateTime.Now.AddDays(3),
                TradeCurrencyCode = "GBP",
                Side = "B",
                Price = 100m,
                SecurityIdentifierType = "Isin",
                SecurityIdentifier = "BMG0464B1072",
                Description = "ARGO",
                BlockGrossAmount = 0.9846330000000000m,
                AllocationGrossAmount = 748424.4700000000000000m,
                TotCommission = 0m,
                SecFeeAmount = 0m,
                OtherFees = 0m,
                PortfolioId = 1064
            };
        }

        public CTMInstInfo GetInstInfo()
        {
            return new CTMInstInfo
            {
                BroadInstType = "Equity",
                CTMInstType = "COMM",
                StrikePrice = 0,
                AlertSecurityType = "EQU",
                AlertCountryCode = "USA"
            };
        }
    }
}
