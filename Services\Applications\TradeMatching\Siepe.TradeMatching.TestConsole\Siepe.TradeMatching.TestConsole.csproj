<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Serilog" Version="4.1.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.RabbitMQ" Version="7.0.2" />
    <PackageReference Include="Serilog.Expressions" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Libraries\Applications\TradeMatching\Siepe.TradeMatching.DataAccess\Siepe.TradeMatching.DataAccess.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Applications\TradeMatching\Siepe.TradeMatching.DataAccess.CTM\Siepe.TradeMatching.DataAccess.CTM.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Applications\TradeMatching\Siepe.TradeMatching.Entities\Siepe.TradeMatching.Entities.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Applications\TradeMatching\Siepe.TradeMatching.Services.CTM\Siepe.TradeMatching.Services.CTM.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Infrastructure\PubSub\Siepe.Infrastructure.PubSub.Common\Siepe.Infrastructure.PubSub.Common.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Infrastructure\PubSub\Siepe.Infrastructure.PubSub.RabbitMq\Siepe.Infrastructure.PubSub.RabbitMq.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Infrastructure\Logging\Siepe.Infrastructure.Logging.Extensions\Siepe.Infrastructure.Logging.Extensions.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Infrastructure\Logging\Siepe.Infrastructure.Logging.QueryLogging.Extensions\Siepe.Infrastructure.Logging.QueryLogging.Extensions.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Infrastructure\Utilities\Siepe.Infrastructure.Utilities.Services\Siepe.Infrastructure.Utilities.Services.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\Siepe.NetCore.Infrastructure\Siepe.NetCore.Infrastructure\Siepe.NetCore.Infrastructure.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\DBUtility\DBUtility.Common\DBUtility.Common.csproj" />
    <ProjectReference Include="..\..\..\..\Libraries\DBUtility\DBUtility.v1\DBUtility.v1.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>