using System.Net;
using System.Reflection;
using Microsoft.Extensions.Options;
using Serilog;
using Siepe.Infrastructure.Logging;
using Siepe.Infrastructure.Logging.Extensions;
using Siepe.Infrastructure.Logging.QueryLogging.Extensions;
using Siepe.Infrastructure.PubSub.Common;
using Siepe.Infrastructure.PubSub.RabbitMq;
using Siepe.Infrastructure.Utilities.Services;
using Siepe.NetCore.Infrastructure.Extensions;
using Siepe.Services.Applications.TradeMatchingService;
using Siepe.Shared.DBUtility.Common;
using Siepe.Shared.DBUtility.v1;
using Siepe.TradeMatching.DataAccess;
using Siepe.TradeMatching.DataAccess.CTM;
using Siepe.TradeMatching.Entities.CTM;
using Siepe.TradeMatching.Services;
using Siepe.TradeMatching.Services.CTM;
using Siepe.TradeMatching.Services.CTM.EventHandlers;
using Siepe.TradeMatching.Services.CTM.MessageHandlers;
using Siepe.TradeMatching.Services.CTM.ResponseMessageProcessors;

const string serviceName = "Trade Matching Service";
Environment.CurrentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);

var builder = Host.CreateApplicationBuilder(args);

Log.Logger = new LoggerConfiguration()
                .ConfigureLogger(serviceName, builder.Configuration)
                .CreateLogger();

builder.Services.AddHttpClient("CtmClient")
    .ConfigureHttpClient((serviceProvider, client) =>
    {
        var settings = serviceProvider.GetRequiredService<IOptions<CtmConfiguration>>().Value;
        client.BaseAddress = new Uri(settings.WebEndPoint);

    })
.ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler
    {
        UseCookies = true,
        CookieContainer = new CookieContainer()
    };
});

builder.Services.AddServiceLogging(builder.Configuration, serviceName);
builder.Services.AddTransient<IDbAccess, SqlDbAccess>(s => new SqlDbAccess("CoreConnectionString", new ConfigConnectionStringProvider(builder.Configuration), new NullQueryLogger(), s.GetRequiredService<ILogger<SqlDbAccess>>()));
builder.Services.AddTransient<IClient, CtmClient>(c =>
{
    var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
    return new CtmClient(c.GetRequiredService<IHttpClientFactory>(), c.GetRequiredService<IMessageContainerService>(), config.Value, c.GetRequiredService<ILogger<CtmClient>>());
});
builder.Services.AddTransient<ICtmService, CtmService>();
builder.Services.AddTransient<IAllocationProvider, CtmAllocationProvider>();
builder.Services.AddTransient<Siepe.TradeMatching.DataAccess.IConfigurationProvider, CtmConfigurationProvider>();
builder.Services.AddTransient<IStatusProvider, CtmStatusProvider>();
builder.Services.AddTransient<IInstrumentProvider, CtmInstrumentProvider>();
builder.Services.AddTransient<IMessageContainerService, MessageContainerService>(c =>
{
    var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
    return new MessageContainerService(config.Value);
});
builder.Services.AddTransient<ITradeMatchService, CtmTradeMatchService>();
builder.Services.LoadConfiguration<CtmConfiguration>(builder.Configuration, "TradeMatchSettings");
builder.Services.AddWindowsService();
builder.Services.AddHostedService<Worker>();
builder.Services.AddHostedService<HeartbeatService>();
builder.Services.AddTransient<IMessageHandler<string>, CancelTradeHandler>();
builder.Services.AddTransient<IMessageHandler<string>, ForceMatchTradeHandler>();
builder.Services.AddTransient<IMessageHandler<string>, RejectTradeHandler>();
builder.Services.AddTransient<IMessageHandler<string>, SubmitSingleTradeHandler>();
builder.Services.AddTransient<IMessageHandler<string>, SubmitTradeHandler>();
builder.Services.AddTransient<IMessageBuilder, CtmMessageBuilder>(c =>
{
    var config = c.GetRequiredService<IOptions<CtmConfiguration>>();
    return new CtmMessageBuilder(config.Value);
});
builder.Services.AddTransient<IResponseMessageProcessor, ResponseMessageProcessor>();
builder.Services.AddTransient<IMessageProcessor, EventProcessor>();
builder.Services.AddTransient<IMessageProcessor, InfoResponseMessageHandler>();
builder.Services.AddTransient<IMessageProcessor, HistoryResponseMessageHandler>();
builder.Services.AddTransient<IMessageProcessor, InfoSettlementResponseMessageHandler>();
builder.Services.AddTransient<IMessageProcessor, InvalidProcessor>();
builder.Services.AddTransient<IMessageProcessor, ValidProcessor>();
builder.Services.AddTransient<IEventHandlerProcessor, EventHandlerProcessor>();

builder.Services.AddTransient<ITLEventHandlerProcessor, TLEventHandlerProcessor>();
builder.Services.AddTransient<ITLEventHandler, CancelEventHandler>();
builder.Services.AddTransient<ITLEventHandler, CompleteEventHandler>();
builder.Services.AddTransient<ITLEventHandler, Siepe.TradeMatching.Services.CTM.EventHandlers.ErrorEventHandler>();
builder.Services.AddTransient<ITLEventHandler, InfoEventHandler>();
builder.Services.AddTransient<ITLEventHandler, CancelMatchAgreed>();
builder.Services.AddTransient<ITLEventHandler, CounterpartyMatchAgreeCancelRequest>();
builder.Services.AddTransient<ITLEventHandler, CounterpartyMatchAgreeCancelRejected>();
builder.Services.AddTransient<ITLEventHandler, MatchAgreeCancelRequest>();
builder.Services.AddTransient<ITLEventHandler, MatchAgreedHandler>();
builder.Services.AddTransient<ITLEventHandler, MatchAgreeRejected>();
builder.Services.AddTransient<ITLEventHandler, MatchedEventHandler>();
builder.Services.AddTransient<ITLEventHandler, MismatchedEventHandler>();
builder.Services.AddTransient<ITLEventHandler, RejectionEventHandler>();
builder.Services.AddTransient<ITLEventHandler, UnmatchedEventHandler>();
builder.Services.AddTransient<ITLEventHandler, WarnEventHandler>();


builder.Services.AddTransient<IEventHandler, CancelEventHandler>();
builder.Services.AddTransient<IEventHandler, Siepe.TradeMatching.Services.CTM.EventHandlers.ErrorEventHandler>();
builder.Services.AddTransient<IEventHandler, ForceMatchEventHandler>();
builder.Services.AddTransient<IEventHandler, InfoEventHandler>();
builder.Services.AddTransient<IEventHandler, MatchedEventHandler>();
builder.Services.AddTransient<IEventHandler, MismatchedEventHandler>();
builder.Services.AddTransient<IEventHandler, RejectionEventHandler>();
builder.Services.AddTransient<IEventHandler, UnmatchedEventHandler>();
builder.Services.AddTransient<IEventHandler, WarnEventHandler>();

builder.Services.AddQueryLogger(serviceName);
builder.Services.AddRabbitMq(builder.Configuration);



try
{
    Log.Information("Starting {ServiceName}...", serviceName);
    var host = builder.Build();
    host.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "{ServiceName} terminated unexpectedly", serviceName);
}
finally
{
    // Make sure to flush and close the logger
    Log.CloseAndFlush();
}