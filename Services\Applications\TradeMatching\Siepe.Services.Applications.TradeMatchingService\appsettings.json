{"ConnectionStrings": {"CoreConnectionString": "Data Source=aristotle-sql-d.aristotle.aws,52155;Initial Catalog=Core;Integrated Security=SSPI;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "WriteTo": [{"Name": "File", "Args": {"path": "logs/tradematching-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}", "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true}}, {"Name": "<PERSON><PERSON>", "Args": {"configureLogger": {"Filter": [{"Name": "ByIncludingOnly", "Args": {"expression": "SourceContext = 'Siepe.TradeMatching.Services.CTM.CtmClient'"}}], "WriteTo": [{"Name": "File", "Args": {"path": "logs/ctmclient-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}}]}, "TradeMatchSettings": {"ClientBIC": "SIEPEHUB", "Username": "cmacs345", "Password": "!t@@5440Harvest", "WebEndPoint": "https://cmidirectu.dtcc.com/cmidirect/", "ClientIdentifier": "SIEPEHUB", "MessageProcessFrequency": 10}, "RabbitMqSettings": {"Host": "b-797230ea-15d3-4efe-a177-2c8be592a3de.mq.us-west-1.amazonaws.com", "Port": 5671, "Queue": "queue.ctm-adapter", "VirtualHost": "Development", "Username": "aristotle-dev", "Password": "<PERSON><PERSON><PERSON>", "UseSsl": true}, "LoggingSettings": {"RabbitMqSettings": {"Enabled": true, "Host": "b-797230ea-15d3-4efe-a177-2c8be592a3de.mq.us-west-1.amazonaws.com", "Exchange": "exchange.logging", "VirtualHost": "Development", "Port": 5671, "Username": "aristotle-dev", "Password": "<PERSON><PERSON><PERSON>"}}}